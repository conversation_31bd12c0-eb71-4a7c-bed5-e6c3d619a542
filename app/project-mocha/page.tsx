// pages/index.tsx
'use client';
import { useState, useEffect } from 'react';
import Head from 'next/head';
import { AnimatePresence, motion } from 'framer-motion';
import QRCode from 'qrcode';
import { createCoffeeConfetti } from '@/components/Confetti';
import { FloatingCoffeeParticles } from '@/components/CoffeeAnimation/FloatingParticles';
import { CoffeeAnimation } from '@/components/CoffeeAnimation';
import { MintingSection } from '@/components/MintingSection';
import { SuccessSection } from '@/components/SuccessSection';
import { TokenData } from '@/components/NFTCard/types';
import { useRouter } from 'next/navigation';
import { useContract } from '@/hooks/useContract';
import { useAddressInfo } from '@/hooks/use-address-info';
import { authService } from '@/app/api/useAuth';

export default function Home() {
    const router = useRouter();
    const [isTokenMinted, setIsTokenMinted] = useState(false);
    const [isMinting, setIsMinting] = useState(false);
    const [tokenData, setTokenData] = useState<TokenData | null>(null);
    const [qrCodeUrl, setQrCodeUrl] = useState('');
    const [mounted, setMounted] = useState(false);
    const [showCoffeeAnimation, setShowCoffeeAnimation] = useState(false);
    const [showFloatingParticles, setShowFloatingParticles] = useState(false);
    const [mintLimitReached, setMintLimitReached] = useState(false);

    const {
        account,
        status,
        connectWallet,
        mintToken,
        isConnected,
        tokenMinted
    } = useContract();

    // Get user's address info to check remaining mints
    const { data: addressInfo, refetch: refetchAddressInfo } = useAddressInfo(account as `0x${string}`);

    useEffect(() => {
        setMounted(true);

        // Check if user is authenticated, if not redirect to homepage
        if (!authService.isAuthenticated()) {
            router.push('/');
            return;
        }
    }, [router]);

    // Check if user has reached minting limit
    useEffect(() => {
        if (addressInfo) {
            setMintLimitReached(addressInfo.remainingMints === 0);
        }
    }, [addressInfo]);

    const handleMintToken = async () => {
        if (!isConnected) {
            await connectWallet();
            if (!isConnected) return;  // If still not connected, exit
        }

        // Check if user has reached minting limit
        if (mintLimitReached || (addressInfo && addressInfo.remainingMints === 0)) {
            alert("You have reached your maximum minting limit. Your minting tokens are complete!");
            return;
        }

        setIsMinting(true);
        setShowCoffeeAnimation(true);

        try {
            // Call the actual mintToken function from the hook
            const receipt = await mintToken();
            console.log("Minting token...", receipt);

            // Refetch address info to get updated remaining mints
            await refetchAddressInfo();

            // Generate token data after successful minting
            const tokenId = 'MOJA' + Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
            const now = new Date();
            const validUntil = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
            const rarity = 'Coffee Cup';

            // Calculate user's total redemptions (based on tokens minted)
            const totalUserTokens = addressInfo?.totalMinted || 1;
            const totalUserRedemptions = totalUserTokens; // Each token gives 1 redemption
            const usedUserRedemptions = 0; // This would come from backend tracking in real implementation

            const newTokenData: TokenData = {
                id: tokenId,
                type: 'Mocha Coffee Token',
                issuer: 'Project Mocha',
                timestamp: Date.now(),
                mintedDate: now.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                }),
                mintedTime: now.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit'
                }),
                validUntil: validUntil.toISOString(),
                validUntilFormatted: validUntil.toLocaleDateString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric'
                }),
                redemptions: 0,
                maxRedemptions: 1,
                totalUserRedemptions: totalUserRedemptions,
                usedUserRedemptions: usedUserRedemptions,
                value: 'FREE',
                rarity: rarity
            };

            // Generate enhanced QR code with wallet address, token ID, and contract address
            const qrData = {
                walletAddress: account,
                tokenId: tokenId,
                contractAddress: process.env.NEXT_PUBLIC_CONTRACT_ADDRESS || '',
                timestamp: Date.now(),
                type: 'coffee-token'
            };

            // Generate proper QR code using QRCode library
            try {
                const qrCodeDataURL = await QRCode.toDataURL(JSON.stringify(qrData), {
                    errorCorrectionLevel: 'M',
                    margin: 2,
                    width: 300,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                });
                setQrCodeUrl(qrCodeDataURL);
            } catch (qrError) {
                console.error('QR Code generation error:', qrError);
                // Fallback to simple string if QR generation fails
                setQrCodeUrl(`${account}-${tokenId}-${process.env.NEXT_PUBLIC_CONTRACT_ADDRESS}`);
            }
            setTokenData(newTokenData);
            setShowCoffeeAnimation(false);

            // Trigger confetti and success animations
            createCoffeeConfetti();
            setShowFloatingParticles(true);
            setIsTokenMinted(true);

            // Stop floating particles after 5 seconds
            setTimeout(() => {
                setShowFloatingParticles(false);
            }, 5000);

        } catch (error) {
            console.error('Error minting token:', error);
            setShowCoffeeAnimation(false);
        } finally {
            setIsMinting(false);
        }
    };

    const resetMinting = () => {
        setIsTokenMinted(false);
        setTokenData(null);
        setQrCodeUrl('');
        setShowFloatingParticles(false);
    };

    if (!mounted) return null;

    return (
        <>
            <Head>
                <title>Project Moja - Premium Coffee Tokens</title>
                <meta name="description" content="Mint your premium Mocha Coffee Token and redeem at participating locations" />
                <meta name="viewport" content="width=device-width, initial-scale=1" />
                <link rel="icon" href="/favicon.ico" />
            </Head>

            <div className="min-h-screen bg-gradient-to-br from-gray-900 via-amber-900 to-orange-900 relative overflow-hidden">
                {/* Background Pattern */}
                <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=`60` height=`60` viewBox=`0 0 60 60` xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f59e0b' fill-opacity='0.03'%3E%3Cpath d='m36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>

                {/* Top Navigation Bar */}
                <motion.nav
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    className="relative z-30 flex justify-between items-center p-6"
                >
                    <div className="flex items-center space-x-3 w-[300px]">
                        <img
                            src='https://res.cloudinary.com/dswyz4vpp/image/upload/v1748690339/crefy/ejr2jnzdotpsziok2itq.png'
                            className='h-[60px]'
                            alt="Project Moja Logo"
                        />
                    </div>

                    <div className="flex items-center space-x-4">
                        <button
                            onClick={() => {
                                authService.logout();
                                router.push('/');
                            }}
                            className="px-4 py-2 text-amber-200 hover:text-white transition-colors duration-300 text-sm font-medium"
                        >
                            Sign Out
                        </button>
                    </div>
                </motion.nav>

                {/* Floating Coffee Particles */}
                <FloatingCoffeeParticles isActive={showFloatingParticles} />

                {/* Floating Orbs */}
                <div className="absolute inset-0 overflow-hidden pointer-events-none">
                    {Array.from({ length: 8 }).map((_, i) => (
                        <motion.div
                            key={i}
                            className="absolute w-32 h-32 bg-gradient-to-r from-amber-400/20 to-orange-400/20 rounded-full blur-xl"
                            animate={{
                                x: [0, 100, 0],
                                y: [0, -50, 0],
                                scale: [1, 1.2, 1],
                            }}
                            transition={{
                                duration: 8 + i * 2,
                                repeat: Infinity,
                                ease: "easeInOut",
                                delay: i * 1.5
                            }}
                            style={{
                                left: `${Math.random() * 100}%`,
                                top: `${Math.random() * 100}%`,
                            }}
                        />
                    ))}
                </div>

                <div className="relative container mx-auto px-4 py-6 max-w-md z-20">
                    {/* Header */}
                    <motion.header
                        initial={{ opacity: 0, y: -20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                        className="text-center mb-8"
                    >
                        {!isTokenMinted ? (
                            <>
                                <h2 className="text-3xl font-bold text-white mb-2">
                                    Mint Your Coffee Token
                                </h2>
                                <p className="text-amber-200 text-sm opacity-90">
                                    Redeem at participating locations worldwide
                                </p>
                                {tokenMinted !== undefined && (
                                    <p className="text-amber-100 mt-2">
                                        Coffee Minted: {tokenMinted.toString()}
                                    </p>
                                )}
                                {status && (
                                    <p className="text-amber-100 mt-1 text-sm">
                                        {status}
                                    </p>
                                )}
                            </>
                        ) : (
                            <h2 className="text-3xl font-bold text-white mb-2">
                                Success! Your Coffee Token is Minted
                            </h2>
                        )}
                    </motion.header>

                    {/* Main Card */}
                    <div className={`bg-white/10 backdrop-blur-2xl ${!isTokenMinted ? 'p-8' : ''} rounded-3xl shadow-2xl shadow-black/20 mb-8 border border-white/20 relative overflow-hidden`}>
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full animate-[shimmer_3s_infinite] pointer-events-none"></div>

                        <motion.div
                            key="mint-section"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0, scale: 0.9 }}
                            transition={{ duration: 0.4 }}
                            className={showCoffeeAnimation ? 'opacity-20 transition-opacity duration-500' : 'opacity-100'}
                        >
                            {/* Coffee Animation Overlay */}
                            <CoffeeAnimation isPlaying={showCoffeeAnimation} size={180} />

                            <AnimatePresence mode="wait">
                                {!isTokenMinted ? (
                                    <MintingSection
                                        isMinting={isMinting}
                                        onMintClick={handleMintToken}
                                        mintLimitReached={mintLimitReached}
                                        remainingMints={addressInfo?.remainingMints}
                                    />
                                ) : tokenData ? (
                                    <SuccessSection
                                        tokenData={tokenData}
                                        qrCodeUrl={qrCodeUrl}
                                        onResetClick={resetMinting}
                                    />
                                ) : null}
                            </AnimatePresence>
                        </motion.div>
                    </div>

                    {/* Footer */}
                    <motion.footer
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.6, delay: 0.8 }}
                        className="text-center text-sm text-gray-400"
                    >
                        <p className="mb-2">
                            &copy; 2025 Project Moja |
                            <span className="text-amber-400 font-semibold"> Crafted with passion</span>
                        </p>
                        <p className="text-xs opacity-75">
                            For coffee enthusiasts worldwide
                        </p>
                        <p className="text-xs opacity-75">
                            Built with ❤️ by <a href="https://utopia.com" className="text-amber-400 font-semibold">Utopia</a>
                        </p>
                    </motion.footer>
                </div>


            </div>
        </>
    );
}