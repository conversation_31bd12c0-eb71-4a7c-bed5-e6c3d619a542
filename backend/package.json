{"name": "crefy-backend", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js", "setup": "node setup.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"cloudinary": "^2.6.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "ethers": "^6.14.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.0", "multer": "^2.0.0", "multer-storage-cloudinary": "^4.0.0", "nodemon": "^3.1.10", "qrcode": "^1.5.4"}, "devDependencies": {"@types/express": "^5.0.2", "@types/node": "^22.15.21", "typescript": "^5.8.3"}}